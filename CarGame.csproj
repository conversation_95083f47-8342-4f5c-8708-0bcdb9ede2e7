<Project Sdk="Godot.NET.Sdk/4.4.1">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <EnableDynamicLoading>true</EnableDynamicLoading>
    <RootNamespace>Game</RootNamespace>
    <Nullable>enable</Nullable>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <DefineConstants Condition="'$(Configuration)'!='ExportRelease' And&#xA;        $(DefineConstants.Contains('GODOT_PC'))">$(DefineConstants);IMGUI</DefineConstants>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="ImGui.NET" Version="1.91.6.1" />
  </ItemGroup>
  <ItemGroup>
    <Content Include=".gitattributes" />
    <Content Include=".gitignore" />
    <Content Include="**\*.tscn" />
    <Content Include="project.godot" />
    <Content Include="README.md" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="addons\" />
  </ItemGroup>
</Project>