using Godot;
using System.Collections.Generic;

namespace CarGame;

public partial class TireTrackSystem : Node2D
{
    [Export] public PackedScene? TireTrackScene { get; set; }
    [Export] public int MaxTireTracks { get; set; } = 200; // Maximum number of tire track segments
    
    private List<TireTrack> _activeTracks = new();
    
    public override void _Ready()
    {
        // Create a default tire track scene if none is provided
        if (TireTrackScene == null)
        {
            CreateDefaultTireTrackScene();
        }
    }
    
    public void CreateTireTrack(Vector2 position, float rotation, float fadeDuration)
    {
        GD.Print($"TireTrackSystem.CreateTireTrack called at {position}, rotation: {rotation}");

        // Remove oldest tracks if we exceed the maximum
        while (_activeTracks.Count >= MaxTireTracks)
        {
            var oldestTrack = _activeTracks[0];
            _activeTracks.RemoveAt(0);
            oldestTrack?.QueueFree();
        }

        // Create new tire track directly
        var tireTrack = new TireTrack();

        // Add the tire track to the level (parent of the car) so it stays in world space
        var level = GetParent().GetParent(); // Car -> Level
        level.AddChild(tireTrack);

        // Use global position directly since we're adding to the level
        tireTrack.Initialize(position, rotation, fadeDuration, this);
        _activeTracks.Add(tireTrack);

        GD.Print($"Total active tracks: {_activeTracks.Count}");
    }
    
    private void CreateDefaultTireTrackScene()
    {
        // Create a simple tire track using a Line2D
        var scene = new PackedScene();
        var tireTrack = new TireTrack();
        scene.Pack(tireTrack);
        TireTrackScene = scene;
    }
    
    public void RemoveTrack(TireTrack track)
    {
        _activeTracks.Remove(track);
    }
}

public partial class TireTrack : Node2D
{
    private ColorRect? _rect;
    private float _fadeDuration;
    private float _currentTime;
    private Color _originalColor = Colors.Red; // Make them red for testing
    private TireTrackSystem? _parentSystem;

    public override void _Ready()
    {
        // Create a colored rectangle for the tire track
        _rect = new ColorRect();
        AddChild(_rect);

        // Configure the rectangle appearance
        _rect.Size = new Vector2(30, 8); // Make them bigger for testing: 30 pixels wide, 8 pixels tall
        _rect.Position = new Vector2(-15, -4); // Center it
        _rect.Color = _originalColor;

        // Debug: Print when tire track is created
        GD.Print($"TireTrack created at {GlobalPosition}");
    }
    
    public void Initialize(Vector2 position, float rotation, float fadeDuration, TireTrackSystem parentSystem)
    {
        GlobalPosition = position;
        Rotation = rotation;
        _fadeDuration = fadeDuration;
        _currentTime = 0.0f;
        _parentSystem = parentSystem;

        GD.Print($"TireTrack initialized at global position {GlobalPosition}");
    }
    
    public override void _Process(double delta)
    {
        _currentTime += (float)delta;
        
        if (_currentTime >= _fadeDuration)
        {
            // Track has fully faded, remove it
            _parentSystem?.RemoveTrack(this);
            QueueFree();
            return;
        }
        
        // Calculate fade alpha (1.0 at start, 0.0 at end)
        float fadeProgress = _currentTime / _fadeDuration;
        float alpha = 1.0f - fadeProgress;
        
        // Apply fade to the rectangle color
        if (_rect != null)
        {
            var fadedColor = _originalColor;
            fadedColor.A = alpha;
            _rect.Color = fadedColor;
        }
    }
}
