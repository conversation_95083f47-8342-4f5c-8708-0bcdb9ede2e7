using Godot;
using System.Collections.Generic;

namespace CarGame;

public partial class TireTrackSystem : Node2D
{
    [Export] public PackedScene? TireTrackScene { get; set; }
    [Export] public int MaxTireTracks { get; set; } = 200; // Maximum number of tire track segments
    
    private List<TireTrack> _activeTracks = new();
    
    public override void _Ready()
    {
        // Create a default tire track scene if none is provided
        if (TireTrackScene == null)
        {
            CreateDefaultTireTrackScene();
        }
    }
    
    public void CreateTireTrack(Vector2 position, float rotation, float fadeDuration)
    {
        if (TireTrackScene == null) return;

        // Remove oldest tracks if we exceed the maximum
        while (_activeTracks.Count >= MaxTireTracks)
        {
            var oldestTrack = _activeTracks[0];
            _activeTracks.RemoveAt(0);
            oldestTrack?.QueueFree();
        }

        // Create new tire track
        var tireTrack = TireTrackScene.Instantiate<TireTrack>();
        AddChild(tireTrack);

        tireTrack.Initialize(position, rotation, fadeDuration);
        _activeTracks.Add(tireTrack);
    }
    
    private void CreateDefaultTireTrackScene()
    {
        // Create a simple tire track using a Line2D
        var scene = new PackedScene();
        var tireTrack = new TireTrack();
        scene.Pack(tireTrack);
        TireTrackScene = scene;
    }
    
    public void RemoveTrack(TireTrack track)
    {
        _activeTracks.Remove(track);
    }
}

public partial class TireTrack : Node2D
{
    private Line2D? _line;
    private float _fadeDuration;
    private float _currentTime;
    private Color _originalColor = Colors.Black;
    private TireTrackSystem? _parentSystem;
    
    public override void _Ready()
    {
        _parentSystem = GetParent<TireTrackSystem>();
        
        // Create the line for the tire track
        _line = new Line2D();
        AddChild(_line);
        
        // Configure the line appearance
        _line.Width = 3.0f;
        _line.DefaultColor = _originalColor;
        _line.Antialiased = true;
        
        // Create a simple tire track shape (two parallel lines)
        _line.AddPoint(new Vector2(-8, 0));
        _line.AddPoint(new Vector2(8, 0));
    }
    
    public void Initialize(Vector2 position, float rotation, float fadeDuration)
    {
        GlobalPosition = position;
        Rotation = rotation;
        _fadeDuration = fadeDuration;
        _currentTime = 0.0f;
    }
    
    public override void _Process(double delta)
    {
        _currentTime += (float)delta;
        
        if (_currentTime >= _fadeDuration)
        {
            // Track has fully faded, remove it
            _parentSystem?.RemoveTrack(this);
            QueueFree();
            return;
        }
        
        // Calculate fade alpha (1.0 at start, 0.0 at end)
        float fadeProgress = _currentTime / _fadeDuration;
        float alpha = 1.0f - fadeProgress;
        
        // Apply fade to the line color
        if (_line != null)
        {
            var fadedColor = _originalColor;
            fadedColor.A = alpha;
            _line.DefaultColor = fadedColor;
        }
    }
}
