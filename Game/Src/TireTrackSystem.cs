using Godot;
using System.Collections.Generic;

namespace CarGame;

public partial class TireTrackSystem : Node2D
{
    [Export] public int MaxTrackPoints { get; set; } = 100; // Maximum points per tire track line
    [Export] public float MinDistanceBetweenPoints { get; set; } = 5.0f; // Minimum distance to add new point

    private List<TireTrackLine> _activeTrackLines = new();
    private TireTrackLine? _currentTrackLine = null;

    public override void _Ready()
    {
        // Nothing to initialize
    }
    
    public void StartTireTrack(Vector2[] wheelPositions, float fadeDuration)
    {
        // End current track if one exists
        EndCurrentTireTrack();

        // Create new tire track line
        _currentTrackLine = new TireTrackLine();

        // Add to the level (parent of the car) so it stays in world space
        var level = GetParent().GetParent(); // Car -> Level
        level.AddChild(_currentTrackLine);

        _currentTrackLine.Initialize(wheelPositions, fadeDuration, this);
        _activeTrackLines.Add(_currentTrackLine);

        // Remove oldest track lines if we have too many
        while (_activeTrackLines.Count > 10) // Limit to 10 track lines
        {
            var oldestTrack = _activeTrackLines[0];
            _activeTrackLines.RemoveAt(0);
            oldestTrack?.QueueFree();
        }
    }

    public void AddPointsToCurrentTrack(Vector2[] wheelPositions)
    {
        _currentTrackLine?.AddPoints(wheelPositions);
    }

    public void EndCurrentTireTrack()
    {
        _currentTrackLine = null;
    }

    public void RemoveTrackLine(TireTrackLine trackLine)
    {
        _activeTrackLines.Remove(trackLine);
    }
}

public partial class TireTrackLine : Node2D
{
    private List<Line2D> _wheelLines = new();
    private float _fadeDuration;
    private float _currentTime;
    private Color _originalColor = Colors.Black;
    private TireTrackSystem? _parentSystem;
    private float _minDistanceBetweenPoints = 5.0f;

    public void Initialize(Vector2[] wheelPositions, float fadeDuration, TireTrackSystem parentSystem)
    {
        _fadeDuration = fadeDuration;
        _currentTime = 0.0f;
        _parentSystem = parentSystem;
        _minDistanceBetweenPoints = parentSystem.MinDistanceBetweenPoints;

        // Create a Line2D for each wheel
        for (int i = 0; i < wheelPositions.Length; i++)
        {
            var line = new Line2D();
            AddChild(line);

            // Configure line appearance
            line.Width = 2.0f;
            line.DefaultColor = _originalColor;
            line.Antialiased = true;
            line.ZIndex = -10;

            // Convert global position to local position
            Vector2 localPos = ToLocal(wheelPositions[i]);
            line.AddPoint(localPos);

            _wheelLines.Add(line);
        }
    }

    public void AddPoints(Vector2[] wheelPositions)
    {
        if (wheelPositions.Length != _wheelLines.Count) return;

        for (int i = 0; i < wheelPositions.Length; i++)
        {
            var line = _wheelLines[i];
            Vector2 localPos = ToLocal(wheelPositions[i]);

            // Only add point if it's far enough from the last point
            if (line.GetPointCount() == 0 || line.GetPointPosition(line.GetPointCount() - 1).DistanceTo(localPos) >= _minDistanceBetweenPoints)
            {
                line.AddPoint(localPos);

                // Remove oldest points if we have too many
                if (line.GetPointCount() > _parentSystem?.MaxTrackPoints)
                {
                    line.RemovePoint(0);
                }
            }
        }
    }

    public override void _Process(double delta)
    {
        _currentTime += (float)delta;

        if (_currentTime >= _fadeDuration)
        {
            // Track has fully faded, remove it
            _parentSystem?.RemoveTrackLine(this);
            QueueFree();
            return;
        }

        // Calculate fade alpha (1.0 at start, 0.0 at end)
        float fadeProgress = _currentTime / _fadeDuration;
        float alpha = 1.0f - fadeProgress;

        // Apply fade to all wheel lines
        var fadedColor = _originalColor;
        fadedColor.A = alpha;

        foreach (var line in _wheelLines)
        {
            line.DefaultColor = fadedColor;
        }
    }
}
