[gd_scene load_steps=4 format=3 uid="uid://c7cu6jgujp2k8"]

[ext_resource type="PackedScene" uid="uid://dj0cpgkssya6y" path="res://Game/Scenes/Car.tscn" id="1_car123"]
[ext_resource type="Script" uid="uid://d3d0riko82n6" path="res://Game/Src/FollowCamera.cs" id="2_camera456"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(100, 20)

[node name="TestLevel" type="Node2D"]

[node name="Car" parent="." instance=ExtResource("1_car123")]
position = Vector2(640, 360)
EngineForce = 1000.0

[node name="Camera2D" type="Camera2D" parent="." node_paths=PackedStringArray("Target")]
script = ExtResource("2_camera456")
Target = NodePath("../Car")

[node name="Obstacles" type="Node2D" parent="."]

[node name="Obstacle1" type="StaticBody2D" parent="Obstacles"]
position = Vector2(400, 200)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Obstacles/Obstacle1"]
shape = SubResource("RectangleShape2D_1")

[node name="ColorRect" type="ColorRect" parent="Obstacles/Obstacle1"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -50.0
offset_top = -10.0
offset_right = 50.0
offset_bottom = 10.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.8, 0.2, 0.2, 1)

[node name="Obstacle2" type="StaticBody2D" parent="Obstacles"]
position = Vector2(800, 500)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Obstacles/Obstacle2"]
shape = SubResource("RectangleShape2D_1")

[node name="ColorRect" type="ColorRect" parent="Obstacles/Obstacle2"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -50.0
offset_top = -10.0
offset_right = 50.0
offset_bottom = 10.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.2, 0.8, 0.2, 1)
