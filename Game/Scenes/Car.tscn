[gd_scene load_steps=11 format=3 uid="uid://dj0cpgkssya6y"]

[ext_resource type="Script" uid="uid://6ujwerl2cxbq" path="res://Game/Src/CarController.cs" id="1_abc123"]
[ext_resource type="Texture2D" uid="uid://d088xlfvqik2r" path="res://Game/Assets/Cars/Car_2_complete_7.png" id="2_f8qr8"]

[sub_resource type="AtlasTexture" id="AtlasTexture_4kchd"]
atlas = ExtResource("2_f8qr8")
region = Rect2(576, 80, 32, 80)

[sub_resource type="AtlasTexture" id="AtlasTexture_if1lu"]
atlas = ExtResource("2_f8qr8")
region = Rect2(608, 80, 32, 80)

[sub_resource type="AtlasTexture" id="AtlasTexture_6fmdf"]
atlas = ExtResource("2_f8qr8")
region = Rect2(640, 80, 32, 80)

[sub_resource type="AtlasTexture" id="AtlasTexture_kqr45"]
atlas = ExtResource("2_f8qr8")
region = Rect2(672, 80, 32, 80)

[sub_resource type="AtlasTexture" id="AtlasTexture_6h2q4"]
atlas = ExtResource("2_f8qr8")
region = Rect2(704, 80, 32, 80)

[sub_resource type="AtlasTexture" id="AtlasTexture_l7xhq"]
atlas = ExtResource("2_f8qr8")
region = Rect2(736, 80, 32, 80)

[sub_resource type="SpriteFrames" id="SpriteFrames_4kchd"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_4kchd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_if1lu")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6fmdf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kqr45")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6h2q4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_l7xhq")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(32, 64)

[node name="Car" type="RigidBody2D"]
mass = 2.0
gravity_scale = 0.0
linear_damp = 1.0
angular_damp = 8.0
script = ExtResource("1_abc123")

[node name="Sprite2D" type="AnimatedSprite2D" parent="."]
sprite_frames = SubResource("SpriteFrames_4kchd")
autoplay = "default"
frame_progress = 0.553531

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_1")
